// Next.js dynamic mini package page
import type { Metadata } from 'next';
import Navbar from '@/components/header';
import PackageHero from '@/components/readpackage/PackageHero';
import PackageOverview from '@/components/readpackage/PackageOverview';
import PackageHighlights from '@/components/readpackage/PackageHighlights';
import PackageItinerary from '@/components/readpackage/PackageItinerary';
import PackageIncludes from '@/components/readpackage/PackageIncludes';
import MiniPackageBookingForm from '@/components/readpackage/MiniPackageBookingForm';
import RecommendedPackages from '@/components/readpackage/RecommendedPackages';
import TourismFooter from '@/components/footer';
import StructuredData from '@/components/common/StructuredData';
import { notFound } from 'next/navigation';

// Enable ISR - regenerate page at most every 5 minutes
export const revalidate = 300

// Allow dynamic params for new mini packages not generated at build time
export const dynamicParams = true
import { generateMetadata as generateSEOMetadata, generateTourSchema, generateBreadcrumbSchema } from '@/lib/seo';
import { createClient } from '@supabase/supabase-js';
import { fetchMiniPackageSafe, generateStaticParamsSafe } from '@/lib/database-utils';

// Initialize Supabase client for static generation
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

// Fetch mini package data directly from database
async function getMiniPackageData(slug: string) {
  try {
    console.log('Fetching mini package data for slug:', slug);

    // Use the safe fetch utility with timeout handling
    const miniPackageData = await fetchMiniPackageSafe(slug);

    if (!miniPackageData) {
      console.log('No mini package found for slug:', slug);
      return null;
    }

    console.log('Mini package found:', miniPackageData.title);

    // Transform the data to match the expected format
    const transformedMiniPackage = {
      id: miniPackageData.id,
      title: miniPackageData.title,
      slug: miniPackageData.slug,
      description: miniPackageData.description,
      overview: miniPackageData.overview,
      location: miniPackageData.location,
      duration: miniPackageData.duration,
      difficulty: miniPackageData.difficulty,
      category: miniPackageData.category,

      // Images
      heroImageUrl: miniPackageData.hero_image_url,
      heroImage: miniPackageData.hero_image_url,
      hero_image_url: miniPackageData.hero_image_url,
      imageUrl: miniPackageData.image_url,
      image_url: miniPackageData.image_url,
      image: miniPackageData.image_url,
      imageAlt: miniPackageData.image_alt,
      heroImageAlt: miniPackageData.hero_image_alt,

      // Pricing
      pricing: {
        solo: { price: miniPackageData.pricing_solo || 0, text: 'Per Person' },
        honeymoon: { price: miniPackageData.pricing_honeymoon || 0, text: 'Per Person' },
        family: { price: miniPackageData.pricing_family || 0, text: 'Per Person' },
        group: { price: miniPackageData.pricing_group || 0, text: 'Per Person' }
      },

      // Lists
      highlights: miniPackageData.highlights || [],
      includes: miniPackageData.includes || [],
      excludes: miniPackageData.excludes || [],
      whatToPack: miniPackageData.packing_list || [],
      packingList: miniPackageData.packing_list || [],

      // SEO
      seoTitle: miniPackageData.seo_title,
      seoDescription: miniPackageData.seo_description,
      seoKeywords: miniPackageData.seo_keywords || [],
      ogTitle: miniPackageData.og_title,
      ogDescription: miniPackageData.og_description,
      ogImageUrl: miniPackageData.og_image_url,
      canonicalUrl: miniPackageData.canonical_url,
      robotsIndex: miniPackageData.robots_index,
      robotsFollow: miniPackageData.robots_follow,
      schemaData: miniPackageData.schema_data,

      // Related content
      contentBlocks: miniPackageData.sas_mini_package_content_blocks?.sort((a, b) => a.sort_order - b.sort_order) || [],
      itinerary: miniPackageData.sas_mini_package_itinerary?.sort((a, b) => a.sort_order - b.sort_order) || [],
      gallery: miniPackageData.sas_mini_package_images?.sort((a, b) => a.sort_order - b.sort_order) || [],

      // Metadata
      status: miniPackageData.status,
      createdAt: miniPackageData.created_at,
      updatedAt: miniPackageData.updated_at,
      publishedAt: miniPackageData.published_at
    };

    return transformedMiniPackage;
  } catch (error) {
    console.error('Error fetching mini package data:', error);
    return null;
  }
}

// Generate static params for all published mini packages
export async function generateStaticParams() {
  const params = await generateStaticParamsSafe('sas_mini_packages', 'status', 'published');
  return params || [];
}

interface PageProps {
  params: Promise<{ slug: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { slug } = await params;
  const miniPackageData = await getMiniPackageData(slug);

  if (!miniPackageData) {
    return generateSEOMetadata({
      title: 'Mini Package Not Found',
      description: 'The requested mini safari package could not be found.',
      url: `/mini-package/${slug}`
    });
  }

  // Calculate lowest price from database pricing structure
  const prices = [
    miniPackageData.pricing.solo,
    miniPackageData.pricing.honeymoon,
    miniPackageData.pricing.family,
    miniPackageData.pricing.group
  ].filter(price => price > 0);
  const lowestPrice = Math.min(...prices);

  // Use user-inputted SEO metadata if available, otherwise fallback to defaults
  if (miniPackageData.seoTitle && miniPackageData.seoDescription) {
    return generateSEOMetadata({
      title: miniPackageData.seoTitle,
      description: miniPackageData.seoDescription,
      keywords: miniPackageData.seoKeywords || [],
      type: 'website',
      url: `/mini-package/${slug}`,
      image: miniPackageData.ogImageUrl || miniPackageData.heroImageUrl,
      price: lowestPrice,
      currency: 'USD',
      availability: 'InStock',
      category: miniPackageData.category
    });
  }

  // Fallback to auto-generated metadata
  return generateSEOMetadata({
    title: `${miniPackageData.title} - African Day Tour | Swift Africa Safaris`,
    description: `${(miniPackageData.overview || miniPackageData.description || '').substring(0, 150)}... Starting from $${lowestPrice}. Book your ${miniPackageData.title} day tour today!`,
    keywords: [
      miniPackageData.title.toLowerCase(),
      `${miniPackageData.title} day tour`,
      `${miniPackageData.location} day tour`,
      `${miniPackageData.category} tour`,
      'African day tour',
      'mini safari package',
      'day trip booking',
      'African adventure',
      miniPackageData.location || 'East Africa',
      `${lowestPrice} day tour`
    ],
    type: 'website',
    url: `/mini-package/${slug}`,
    image: miniPackageData.heroImageUrl || miniPackageData.imageUrl,
    price: lowestPrice,
    currency: 'USD',
    availability: 'InStock',
    category: miniPackageData.category
  });
}

const ReadMiniPackage = async ({ params }: PageProps) => {
  const { slug } = await params;
  const miniPackageData = await getMiniPackageData(slug);

  if (!miniPackageData) {
    notFound();
  }

  // Calculate lowest price from database pricing structure
  const prices = [
    miniPackageData.pricing.solo,
    miniPackageData.pricing.honeymoon,
    miniPackageData.pricing.family,
    miniPackageData.pricing.group
  ].filter(price => price > 0);
  const lowestPrice = Math.min(...prices);

  const breadcrumbSchema = generateBreadcrumbSchema([
    { name: 'Home', url: '/' },
    { name: 'Day Tours', url: '/mini-package' },
    { name: miniPackageData.title, url: `/mini-package/${slug}` }
  ]);

  // Use user-inputted schema if available, otherwise generate default
  let tourSchema;
  if (miniPackageData.schemaData) {
    tourSchema = {
      '@context': 'https://schema.org',
      ...miniPackageData.schemaData,
      url: `https://swiftafricasafaris.com/mini-package/${slug}`,
      offers: {
        ...miniPackageData.schemaData.offers,
        price: lowestPrice.toString(),
        url: `https://swiftafricasafaris.com/mini-package/${slug}`
      }
    };
  } else {
    // Fallback to auto-generated schema
    tourSchema = generateTourSchema({
      name: miniPackageData.title,
      description: miniPackageData.overview || miniPackageData.description,
      image: miniPackageData.heroImageUrl || miniPackageData.imageUrl,
      price: lowestPrice,
      currency: 'USD',
      duration: miniPackageData.duration || '4 Hours',
      location: miniPackageData.location || 'East Africa',
      url: `/mini-package/${slug}`,
      rating: 4.8,
      reviewCount: 45
    });
  }

  return (
    <div>
      <StructuredData data={[breadcrumbSchema, tourSchema]} />
      <Navbar />
      <div className="min-h-screen bg-[var(--primary-background)]">
        <PackageHero
          image={miniPackageData.heroImageUrl || miniPackageData.heroImage || miniPackageData.hero_image_url || miniPackageData.imageUrl || miniPackageData.image_url || miniPackageData.image}
          title={miniPackageData.title}
          packageType="mini"
          duration={miniPackageData.duration}
          location={miniPackageData.location}
          difficulty={miniPackageData.difficulty}
          startingPrice={lowestPrice > 0 ? lowestPrice : null}
          breadcrumbs={[
            { name: 'Home', url: '/' },
            { name: 'Day Tours', url: '/mini-package' },
            { name: miniPackageData.title, url: `/mini-package/${slug}` }
          ]}
        />
        <div className="container mx-auto px-4 py-8 flex flex-col lg:flex-row gap-8">
          <div className="lg:w-2/3">
            <PackageOverview
              contentBlocks={miniPackageData.contentBlocks || []}
            />
            <PackageHighlights
              highlights={miniPackageData.highlights || []}
              whatToPack={miniPackageData.whatToPack || miniPackageData.packingList || []}
            />
            <PackageItinerary
              itinerary={miniPackageData.itinerary || []}
            />
            <PackageIncludes
              includes={miniPackageData.includes || []}
              excludes={miniPackageData.excludes || []}
            />
          </div>
          <div className="lg:w-1/3">
            <div className="sticky top-24">
              <MiniPackageBookingForm
                pricing={miniPackageData.pricing}
                packageTitle={miniPackageData.title}
                packageId={miniPackageData.id}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Recommended Packages Section */}
      <RecommendedPackages
        currentPackageSlug={slug}
        currentLocation={miniPackageData.location}
        currentCategory={miniPackageData.category}
      />

      <TourismFooter />
    </div>
  );
};

export default ReadMiniPackage;
