'use client';
import React, { useState } from 'react';
import { getTemporaryFallbackImage } from '@/lib/fallback-images';
import LazyImage from '../common/LazyImage';
import { MapPinIcon, ClockIcon, StarIcon } from '@heroicons/react/24/outline';

const PackageHero = ({
  image,
  title,
  packageType = 'regular', // 'regular' or 'mini'
  duration,
  location,
  difficulty,
  startingPrice
}) => {
  const [imageError, setImageError] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  // Debug: Log the image URL
  console.log('PackageHero - Image URL:', image);
  console.log('PackageHero - Title:', title);

  const handleImageError = () => {
    console.error('PackageHero - Image failed to load:', image);
    setImageError(true);
  };

  const handleImageLoad = () => {
    console.log('PackageHero - Image loaded successfully:', image);
    setImageLoaded(true);
  };

  const scrollToBooking = () => {
    const bookingSection = document.querySelector('.booking-form-section');
    if (bookingSection) {
      bookingSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  // Fallback image from Supabase storage
  const fallbackImage = getTemporaryFallbackImage();
  const imageUrl = image || fallbackImage.url;

  // Package type configuration
  const packageConfig = {
    regular: {
      badge: 'Safari Package',
      badgeColor: 'bg-[var(--btn)] text-white',
      durationLabel: 'Days',
      ctaText: 'Book Safari'
    },
    mini: {
      badge: 'Day Tour',
      badgeColor: 'bg-[var(--accent)] text-white',
      durationLabel: 'Hours',
      ctaText: 'Book Day Tour'
    }
  };

  const config = packageConfig[packageType] || packageConfig.regular;

  return (
    <section className="relative h-[60vh] md:h-[65vh] lg:h-[70vh] w-full overflow-hidden">
      {/* Hero Image */}
      <div className="h-full w-full">
        <LazyImage
          src={imageUrl}
          alt={title || 'Package hero image'}
          className="h-full w-full object-cover transition-transform duration-700 hover:scale-105"
          width={1920}
          height={700}
          priority={true}
          skeletonVariant="hero"
          onLoad={handleImageLoad}
          onError={handleImageError}
        />
      </div>

      {/* Enhanced Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-black/40 via-black/50 to-black/75" />

      {/* Content Container */}
      <div className="absolute inset-0 z-10 flex items-center justify-center p-4 md:p-6 lg:p-8">

        {/* Main Content */}
        <div className="w-full">
          <div className="text-center text-white max-w-4xl mx-auto">

            {/* Package Type Badge */}
            <div className="inline-flex items-center mb-4">
              <span className={`px-3 py-1 rounded-full text-xs font-bold uppercase tracking-wide ${config.badgeColor}`}>
                {config.badge}
              </span>
            </div>

            {/* Title */}
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 leading-tight animate-fadeInUp">
              {title}
            </h1>

            {/* Quick Info Cards */}
            {(duration || location || difficulty || startingPrice) && (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-4 mb-8 max-w-2xl mx-auto">
                {duration && (
                  <div className="hero-info-card bg-white/10 backdrop-blur-sm rounded-lg p-3 text-center border border-white/20">
                    <ClockIcon className="w-5 h-5 mx-auto mb-1 text-white/80" />
                    <div className="text-xs text-white/70 uppercase tracking-wide">{config.durationLabel}</div>
                    <div className="text-sm font-semibold">{duration}</div>
                  </div>
                )}

                {location && (
                  <div className="hero-info-card bg-white/10 backdrop-blur-sm rounded-lg p-3 text-center border border-white/20">
                    <MapPinIcon className="w-5 h-5 mx-auto mb-1 text-white/80" />
                    <div className="text-xs text-white/70 uppercase tracking-wide">Location</div>
                    <div className="text-sm font-semibold line-clamp-1">{location}</div>
                  </div>
                )}

                {difficulty && (
                  <div className="hero-info-card bg-white/10 backdrop-blur-sm rounded-lg p-3 text-center border border-white/20">
                    <StarIcon className="w-5 h-5 mx-auto mb-1 text-white/80" />
                    <div className="text-xs text-white/70 uppercase tracking-wide">Difficulty</div>
                    <div className="text-sm font-semibold">{difficulty}</div>
                  </div>
                )}

                {startingPrice && (
                  <div className="hero-info-card bg-white/10 backdrop-blur-sm rounded-lg p-3 text-center border border-white/20">
                    <div className="text-xs text-white/70 uppercase tracking-wide">From</div>
                    <div className="text-sm font-semibold">${startingPrice}</div>
                  </div>
                )}
              </div>
            )}

            {/* Call to Action Button */}
            <button
              onClick={scrollToBooking}
              className="hero-cta-button inline-flex items-center px-8 py-3 text-white font-semibold rounded-lg
                         focus:outline-none focus:ring-4 focus:ring-orange-500/50 shadow-lg"
              aria-label={`${config.ctaText} - Scroll to booking form`}
            >
              {config.ctaText}
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PackageHero;
