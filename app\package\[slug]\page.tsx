// Next.js dynamic package page
import type { Metadata } from 'next';
import Navbar from '@/components/header';
import PackageHero from '@/components/readpackage/PackageHero';
import PackageOverview from '@/components/readpackage/PackageOverview';
import PackageHighlights from '@/components/readpackage/PackageHighlights';
import PackageItinerary from '@/components/readpackage/PackageItinerary';
import PackageIncludes from '@/components/readpackage/PackageIncludes';
import BookingForm from '@/components/readpackage/BookingForm';
import RecommendedPackages from '@/components/readpackage/RecommendedPackages';
import TourismFooter from '@/components/footer';
import StructuredData from '@/components/common/StructuredData';
import { notFound } from 'next/navigation';

// Enable ISR - regenerate page at most every 5 minutes
export const revalidate = 300

// Allow dynamic params for new packages not generated at build time
export const dynamicParams = true
import { generateMetadata as generateSEOMetadata, generateTourSchema, generateBreadcrumbSchema } from '@/lib/seo';
import { supabase } from '@/lib/supabase';
import { generateStaticParamsSafe, withTimeout } from '@/lib/database-utils';

// Fetch package data directly from database
async function getPackageData(slug: string) {
  try {
    console.log('Fetching package data for slug:', slug);

    // Test basic connection first with timeout
    const testResult = await withTimeout(
      async () => {
        const { data, error } = await supabase
          .from('sas_packages')
          .select('id, title, slug')
          .limit(1);

        if (error) throw error;
        return data;
      },
      10000, // 10 second timeout
      'Supabase connection test'
    );

    if (!testResult) {
      console.error('Supabase connection test failed - no data returned');
      return null;
    }

    console.log('Supabase connection test successful, found packages:', testResult.length);

    // Check if the specific package exists first with timeout
    const packageCheck = await withTimeout(
      async () => {
        const { data, error } = await supabase
          .from('sas_packages')
          .select('id, title, slug, status')
          .eq('slug', slug);

        if (error) throw error;
        return data;
      },
      10000,
      `Package existence check for slug: ${slug}`
    );

    if (!packageCheck) {
      console.error('Package existence check failed');
      return null;
    }

    console.log('Package check result:', {
      slug: slug,
      found: packageCheck.length,
      packages: packageCheck
    });

    if (packageCheck.length === 0) {
      console.log('No package found with slug:', slug);
      return null;
    }

    // Fetch package with all related data (only if active)
    const packageData = await withTimeout(
      async () => {
        const { data, error } = await supabase
          .from('sas_packages')
          .select(`
            *,
            sas_package_content_blocks (
              id,
              block_type,
              content,
              content_data,
              image_url,
              image_alt,
              image_caption,
              sort_order
            ),
            sas_package_itinerary (
              id,
              day_number,
              title,
              description,
              activities,
              accommodation,
              meals,
              sort_order
            ),
            sas_package_images (
              id,
              image_url,
              image_alt,
              caption,
              sort_order,
              is_featured
            )
          `)
          .eq('slug', slug)
          .eq('status', 'active') // Only return active packages
          .single();

        if (error) {
          console.error('Supabase query error details:', {
            message: error.message,
            details: error.details,
            hint: error.hint,
            code: error.code
          });
          throw error;
        }
        return data;
      },
      20000, // 20 second timeout for complex query
      `Package data fetch for slug: ${slug}`
    );

    if (!packageData) {
      console.log('Package data fetch failed or no package found for slug:', slug);
      return null;
    }

    console.log('Package found:', packageData.title);
    console.log('Package pricing structure:', {
      solo: packageData.pricing_solo,
      honeymoon: packageData.pricing_honeymoon,
      family: packageData.pricing_family,
      group: packageData.pricing_group
    });

    // Transform the data to match the expected format
    const transformedPackage = {
      id: packageData.id,
      title: packageData.title,
      slug: packageData.slug,
      description: packageData.description,
      overview: packageData.overview,
      location: packageData.location,
      duration: packageData.duration,
      difficulty: packageData.difficulty,
      category: packageData.category,

      // Images
      heroImageUrl: packageData.hero_image_url,
      heroImage: packageData.hero_image_url,
      hero_image_url: packageData.hero_image_url,
      imageUrl: packageData.image_url,
      image_url: packageData.image_url,
      image: packageData.image_url,
      imageAlt: packageData.image_alt,
      heroImageAlt: packageData.hero_image_alt,

      // Pricing
      pricing: {
        solo: { price: packageData.pricing_solo || 0, text: 'Per Person' },
        honeymoon: { price: packageData.pricing_honeymoon || 0, text: 'Per Person' },
        family: { price: packageData.pricing_family || 0, text: 'Per Person' },
        group: { price: packageData.pricing_group || 0, text: 'Per Person' }
      },

      // Lists
      highlights: packageData.highlights || [],
      includes: packageData.includes || [],
      excludes: packageData.excludes || [],
      whatToPack: packageData.packing_list || [],
      packingList: packageData.packing_list || [],

      // SEO
      seoTitle: packageData.seo_title,
      seoDescription: packageData.seo_description,
      seoKeywords: packageData.seo_keywords || [],
      ogTitle: packageData.og_title,
      ogDescription: packageData.og_description,
      ogImageUrl: packageData.og_image_url,
      canonicalUrl: packageData.canonical_url,
      robotsIndex: packageData.robots_index,
      robotsFollow: packageData.robots_follow,
      schemaData: packageData.schema_data,

      // Related content
      contentBlocks: packageData.sas_package_content_blocks?.sort((a: any, b: any) => a.sort_order - b.sort_order) || [],
      itinerary: packageData.sas_package_itinerary?.sort((a: any, b: any) => a.sort_order - b.sort_order) || [],
      gallery: packageData.sas_package_images?.sort((a: any, b: any) => a.sort_order - b.sort_order) || [],

      // Metadata
      status: packageData.status,
      createdAt: packageData.created_at,
      updatedAt: packageData.updated_at,
      publishedAt: packageData.published_at
    };

    return transformedPackage;
  } catch (error) {
    console.error('Error fetching package data:', {
      error: error,
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      slug: slug
    });
    return null;
  }
}

// Generate static params for all active packages
export async function generateStaticParams() {
  const params = await generateStaticParamsSafe('sas_packages', 'status', 'active');
  return params || [];
}

interface PageProps {
  params: Promise<{ slug: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}



export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { slug } = await params;
  const packageData = await getPackageData(slug);

  if (!packageData) {
    return generateSEOMetadata({
      title: 'Package Not Found',
      description: 'The requested safari package could not be found.',
      url: `/package/${slug}`
    });
  }

  // Calculate lowest price from database pricing structure
  const prices = [
    packageData.pricing.solo?.price || packageData.pricing.solo,
    packageData.pricing.honeymoon?.price || packageData.pricing.honeymoon,
    packageData.pricing.family?.price || packageData.pricing.family,
    packageData.pricing.group?.price || packageData.pricing.group
  ].filter((price: any) => typeof price === 'number' && price > 0);
  const lowestPrice = prices.length > 0 ? Math.min(...prices) : 0;

  // Use user-inputted SEO metadata if available, otherwise fallback to defaults
  if (packageData.seoTitle && packageData.seoDescription) {
    return generateSEOMetadata({
      title: packageData.seoTitle,
      description: packageData.seoDescription,
      keywords: packageData.seoKeywords || [],
      type: 'website',
      url: `/package/${slug}`,
      image: packageData.ogImageUrl || packageData.heroImageUrl,
      price: lowestPrice,
      currency: 'USD',
      availability: 'InStock',
      category: packageData.category
    });
  }

  // Fallback to auto-generated metadata
  return generateSEOMetadata({
    title: `${packageData.title} - African Safari Package | Swift Africa Safaris`,
    description: `${(packageData.overview || packageData.description || '').substring(0, 150)}... Starting from $${lowestPrice}. Book your ${packageData.title} adventure today!`,
    keywords: [
      packageData.title.toLowerCase(),
      `${packageData.title} safari`,
      `${packageData.location} safari`,
      `${packageData.category} tour`,
      'African safari package',
      'wildlife tour',
      'safari booking',
      'African adventure',
      packageData.location || 'East Africa',
      `${lowestPrice} safari package`
    ],
    type: 'website',
    url: `/package/${slug}`,
    image: packageData.heroImageUrl || packageData.imageUrl,
    price: lowestPrice,
    currency: 'USD',
    availability: 'InStock',
    category: packageData.category
  });
}

const ReadPackage = async ({ params }: PageProps) => {
  const { slug } = await params;
  const packageData = await getPackageData(slug);

  if (!packageData) {
    notFound();
  }

  // Calculate lowest price from database pricing structure
  const prices = [
    packageData.pricing.solo?.price || packageData.pricing.solo,
    packageData.pricing.honeymoon?.price || packageData.pricing.honeymoon,
    packageData.pricing.family?.price || packageData.pricing.family,
    packageData.pricing.group?.price || packageData.pricing.group
  ].filter((price: any) => typeof price === 'number' && price > 0);
  const lowestPrice = prices.length > 0 ? Math.min(...prices) : 0;

  const breadcrumbSchema = generateBreadcrumbSchema([
    { name: 'Home', url: '/' },
    { name: 'Safari Packages', url: '/package' },
    { name: packageData.title, url: `/package/${slug}` }
  ]);

  // Use user-inputted schema if available, otherwise generate default
  let tourSchema;
  if (packageData.schemaData) {
    tourSchema = {
      '@context': 'https://schema.org',
      ...packageData.schemaData,
      url: `https://swiftafricasafaris.com/package/${slug}`,
      offers: {
        ...packageData.schemaData.offers,
        price: lowestPrice.toString(),
        url: `https://swiftafricasafaris.com/package/${slug}`
      }
    };
  } else {
    // Fallback to auto-generated schema
    tourSchema = generateTourSchema({
      name: packageData.title,
      description: packageData.overview || packageData.description,
      image: packageData.heroImageUrl || packageData.imageUrl,
      price: lowestPrice,
      currency: 'USD',
      duration: packageData.duration || '4 Days',
      location: packageData.location || 'East Africa',
      url: `/package/${slug}`,
      rating: 4.8,
      reviewCount: 45
    });
  }

  return (
    <div>
      <StructuredData data={[breadcrumbSchema, tourSchema]} />
      <Navbar />
      <div className="min-h-screen bg-[var(--primary-background)]">
        <PackageHero
          image={packageData.heroImageUrl || packageData.heroImage || packageData.hero_image_url || packageData.imageUrl || packageData.image_url || packageData.image}
          title={packageData.title}
          packageType="regular"
          duration={packageData.duration}
          location={packageData.location}
          difficulty={packageData.difficulty}
          startingPrice={lowestPrice > 0 ? lowestPrice : null}
        />
        <div className="container mx-auto px-4 py-8 flex flex-col lg:flex-row gap-8">
          <div className="lg:w-2/3">
            <PackageOverview
              contentBlocks={packageData.contentBlocks || []}
            />
            <PackageHighlights
              highlights={packageData.highlights || []}
              whatToPack={packageData.whatToPack || packageData.packingList || []}
            />
            <PackageItinerary
              itinerary={packageData.itinerary || []}
            />
            <PackageIncludes
              includes={packageData.includes || []}
              excludes={packageData.excludes || []}
            />
          </div>
          <div className="lg:w-1/3">
            <div className="sticky top-24">
              <BookingForm
                pricing={packageData.pricing}
                packageTitle={packageData.title}
                packageId={packageData.id}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Recommended Packages Section */}
      <RecommendedPackages
        currentPackageSlug={slug}
        currentLocation={packageData.location}
        currentCategory={packageData.category}
      />

      <TourismFooter />
    </div>
  );
};

export default ReadPackage;
